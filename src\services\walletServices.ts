import User from '../models/User';

export const transferFunds = async (senderId: string, receiverEmail: string, amount: number) => {
  const sender = await User.findById(senderId);
  const receiver = await User.findOne({ email: receiverEmail });

  if (!sender || !receiver) throw new Error('Users not found');
  if (sender.balance < amount) throw new Error('Insufficient balance');

  sender.balance -= amount;
  receiver.balance += amount;

  await sender.save();
  await receiver.save();

  return { from: sender.email, to: receiver.email, amount };
};
