import express from 'express';
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import routes from './routes';
import swaggerUi from 'swagger-ui-express';
import swaggerDoc from './docs/swagger.json';
import errorHandler from './middlewares/errorHandler';
import logger from './middlewares/logger';

dotenv.config();

const app = express();
app.use(express.json());
app.use(logger); // log all requests

app.use('/api', routes); // All our app routes
app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerDoc)); // API guidebook
app.use(errorHandler); // catch any errors

mongoose.connect(process.env.MONGO_URI!).then(() => {
  console.log('MongoDB connected');
});

export default app;
